"""Maintenance management widget for the equipment inventory application."""
import logging
from datetime import datetime, date, timedelta

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
                           QLabel, QPushButton, QLineEdit, QComboBox, 
                           QSpinBox, QDoubleSpinBox, QDateEdit, QCheckBox,
                           QGroupBox, QFormLayout, QMessageBox, QSplitter,
                           QTextEdit, QDialog, QTabWidget, QDialogButtonBox)
from PyQt5.QtCore import Qt, QDate

import database
import utils
import config
from models import Equipment, Maintenance
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel
from ui.common_styles import apply_combobox_hover_fix

# Configure logger
logger = logging.getLogger('maintenance_widget')

class MaintenanceWidget(QWidget):
    """Main widget containing all maintenance sub-tabs."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the main maintenance widget with sub-tabs."""
        # Create main layout
        main_layout = QVBoxLayout(self)
        
        # Create tab widget for maintenance categories
        self.tab_widget = QTabWidget()
        
        # Create sub-widgets for each maintenance category
        self.tm1_widget = MaintenanceSubWidget(category='TM-1', parent=self)
        self.tm2_widget = MaintenanceSubWidget(category='TM-2', parent=self)
        
        # Regular maintenance tab with its own sub-tabs
        self.regular_maintenance_widget = RegularMaintenanceWidget(parent=self)
        
        # History tab for archived maintenance records
        from ui.maintenance_history_widget import MaintenanceHistoryWidget
        self.history_widget = MaintenanceHistoryWidget(parent=self)
        
        # Add tabs
        self.tab_widget.addTab(self.tm1_widget, "TM-1")
        self.tab_widget.addTab(self.tm2_widget, "TM-2")
        self.tab_widget.addTab(self.regular_maintenance_widget, "Regular Maintenance")
        self.tab_widget.addTab(self.history_widget, "History")
        
        # Connect tab changed signal
        self.tab_widget.currentChanged.connect(self.tab_changed)
        
        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)
        
    def load_data(self):
        """Load data for all maintenance sub-tabs."""
        logger.info("Loading data for all maintenance tabs")
        try:
            # Load data for currently visible tab first
            current_widget = self.tab_widget.currentWidget()
            if hasattr(current_widget, 'load_data'):
                current_widget.load_data()
                
            # Load data for other tabs
            for i in range(self.tab_widget.count()):
                widget = self.tab_widget.widget(i)
                if widget != current_widget and hasattr(widget, 'load_data'):
                    widget.load_data()
                    
        except Exception as e:
            logger.error(f"Error loading maintenance data: {e}")
    
    def tab_changed(self, index):
        """Handle tab change event."""
        current_widget = self.tab_widget.widget(index)
        if hasattr(current_widget, 'load_data'):
            current_widget.load_data()
    
    def switch_to_category(self, category):
        """Switch to the specified maintenance category tab."""
        try:
            if category == 'TM-1':
                self.tab_widget.setCurrentIndex(0)  # TM-1 tab
            elif category == 'TM-2':
                self.tab_widget.setCurrentIndex(1)  # TM-2 tab
            elif category in ['Yearly', 'Monthly']:
                self.tab_widget.setCurrentIndex(2)  # Regular Maintenance tab
                # Set the sub-tab within Regular Maintenance
                regular_widget = self.tab_widget.widget(2)
                if hasattr(regular_widget, 'set_active_tab'):
                    regular_widget.set_active_tab(category)
            
            # Load data for the current tab
            current_widget = self.tab_widget.currentWidget()
            if hasattr(current_widget, 'load_data'):
                current_widget.load_data()
                
            logger.info(f"Switched to maintenance category: {category}")
            
        except Exception as e:
            logger.error(f"Error switching to maintenance category {category}: {e}")
    
    def create_maintenance_from_equipment(self, equipment_id, maintenance_category):
        """Create new maintenance record from equipment tab."""
        try:
            # Switch to appropriate tab
            tab_mapping = {
                'TM-1': 0,
                'TM-2': 1,
                'Yearly': 2,  # Regular Maintenance tab
                'Monthly': 2  # Regular Maintenance tab
            }
            
            tab_index = tab_mapping.get(maintenance_category, 0)
            self.tab_widget.setCurrentIndex(tab_index)
            
            # If it's yearly or monthly, switch to appropriate sub-tab
            if maintenance_category in ['Yearly', 'Monthly']:
                self.regular_maintenance_widget.set_active_tab(maintenance_category)
                
            # Get the appropriate widget and create maintenance
            current_widget = self.tab_widget.currentWidget()
            if hasattr(current_widget, 'create_maintenance_from_equipment'):
                current_widget.create_maintenance_from_equipment(equipment_id, maintenance_category)
            elif hasattr(current_widget, 'get_current_sub_widget'):
                # For regular maintenance widget with sub-tabs
                sub_widget = current_widget.get_current_sub_widget()
                if sub_widget and hasattr(sub_widget, 'create_maintenance_from_equipment'):
                    sub_widget.create_maintenance_from_equipment(equipment_id, maintenance_category)
                    
        except Exception as e:
            logger.error(f"Error creating maintenance from equipment: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to create maintenance record: {str(e)}",
                QMessageBox.StandardButton.Ok
            )

class RegularMaintenanceWidget(QWidget):
    """Widget for regular maintenance with Yearly and Monthly sub-tabs."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the regular maintenance widget with yearly/monthly tabs."""
        # Create main layout
        main_layout = QVBoxLayout(self)
        
        # Create tab widget for yearly/monthly
        self.sub_tab_widget = QTabWidget()
        
        # Create sub-widgets
        self.yearly_widget = MaintenanceSubWidget(category='Yearly', parent=self)
        self.monthly_widget = MaintenanceSubWidget(category='Monthly', parent=self)
        
        # Add tabs (Yearly is default)
        self.sub_tab_widget.addTab(self.yearly_widget, "Yearly")
        self.sub_tab_widget.addTab(self.monthly_widget, "Monthly")
        
        # Set Yearly as default selection
        self.sub_tab_widget.setCurrentIndex(0)
        
        # Connect tab changed signal
        self.sub_tab_widget.currentChanged.connect(self.sub_tab_changed)
        
        # Add sub-tab widget to main layout
        main_layout.addWidget(self.sub_tab_widget)
        
    def load_data(self):
        """Load data for both yearly and monthly tabs."""
        try:
            self.yearly_widget.load_data()
            self.monthly_widget.load_data()
        except Exception as e:
            logger.error(f"Error loading regular maintenance data: {e}")
    
    def sub_tab_changed(self, index):
        """Handle sub-tab change event."""
        current_widget = self.sub_tab_widget.widget(index)
        if hasattr(current_widget, 'load_data'):
            current_widget.load_data()
    
    def set_active_tab(self, category):
        """Set the active sub-tab based on category."""
        if category == 'Yearly':
            self.sub_tab_widget.setCurrentIndex(0)
        elif category == 'Monthly':
            self.sub_tab_widget.setCurrentIndex(1)
    
    def get_current_sub_widget(self):
        """Get the currently active sub-widget."""
        return self.sub_tab_widget.currentWidget()

class MaintenanceSubWidget(QWidget):
    """Individual maintenance widget for each category (TM-1, TM-2, Yearly, Monthly)."""
    
    def __init__(self, category, parent=None):
        super().__init__(parent)
        self.category = category
        self.equipment_cache = {}  # Cache equipment data
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the maintenance sub-widget UI (same as original MaintenanceWidget)."""
        # Create main layout
        main_layout = QHBoxLayout(self)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Create left panel (maintenance list)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Add category label
        category_config = config.MAINTENANCE_CATEGORIES.get(self.category, {'display': self.category})
        category_label = QLabel(f"<h3>{category_config['display']}</h3>")
        left_layout.addWidget(category_label)
        
        # Create filter controls
        filter_layout = QGridLayout()

        filter_layout.addWidget(QLabel("Status Filter:"), 0, 0)
        self.filter_status = QComboBox()
        self.filter_status.addItems([
            "All",
            "Upcoming", 
            "Scheduled", 
            "Completed", 
            "Overdue", 
            "Warning", 
            "Critical"
        ])
        self.filter_status.currentTextChanged.connect(self.load_data)
        filter_layout.addWidget(self.filter_status, 0, 1)

        # Apply the same styling to fix hover issues
        apply_combobox_hover_fix(self.filter_status)

        # Remove the days filter completely since we're using status-based filtering

        left_layout.addLayout(filter_layout)

        # Create enhanced paginated table for maintenance list with BA filtering and grouping
        from ui.paginated_table_widget import PaginatedTableWidget
        self.maintenance_table = PaginatedTableWidget(
            page_size=100,
            max_total_rows=50000,
            enable_ba_filter=True,
            enable_ba_grouping=True,
            show_vintage_button=False  # Disable vintage age button for maintenance table
        )
        self.maintenance_table.row_selected.connect(self.maintenance_selected_enhanced)
        left_layout.addWidget(self.maintenance_table)
        
        # Create buttons for maintenance actions
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("Add New")
        self.edit_button = QPushButton("Edit")
        self.complete_button = QPushButton("Complete")
        self.delete_button = QPushButton("Delete")
        
        # Initial button states
        self.edit_button.setEnabled(False)
        self.complete_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        
        # Style the complete button
        self.complete_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        # Connect button signals
        self.add_button.clicked.connect(self.add_maintenance)
        self.edit_button.clicked.connect(self.edit_maintenance)
        self.complete_button.clicked.connect(self.complete_maintenance)
        self.delete_button.clicked.connect(self.delete_maintenance)
        
        # Add archive button
        self.archive_button = QPushButton("📦 Archive")
        self.archive_button.setToolTip("Archive completed maintenance records")
        self.archive_button.clicked.connect(self.archive_completed)
        self.archive_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.complete_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.archive_button)
        
        left_layout.addLayout(button_layout)
        
        # Create right panel (maintenance details)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Add maintenance details form
        self.create_maintenance_form(right_layout)
        
        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        
        # Set initial sizes
        splitter.setSizes([400, 600])
        
        # Add splitter to main layout
        main_layout.addWidget(splitter)
    
    def create_maintenance_form(self, parent_layout):
        """Create the maintenance details form."""
        # Create form group
        form_group = QGroupBox("Maintenance Details")
        form_layout = QFormLayout()
        
        # Create form fields
        self.id_field = QLineEdit()
        self.id_field.setReadOnly(True)
        
        self.equipment_field = QComboBox()
        self.equipment_display = QLabel()
        self.equipment_display.setStyleSheet("font-size: 10pt; font-family: 'Segoe UI'; padding: 2px 4px;")
        self.equipment_display.setMinimumHeight(24)
        form_layout.addRow("Equipment:", self.equipment_display)
        form_layout.addRow("", self.equipment_field)  # Empty label for alignment
        
        self.maintenance_type_field = QComboBox()
        # Set maintenance types based on category
        maintenance_types = config.MAINTENANCE_TYPES.get(self.category, ["Other"])
        self.maintenance_type_field.addItems(maintenance_types)
        self.maintenance_type_field.setEditable(True)
        
        # Apply proper styling to fix hover issues
        apply_combobox_hover_fix(self.equipment_field)
        apply_combobox_hover_fix(self.maintenance_type_field)
        
        self.due_date_field = QDateEdit()
        self.due_date_field.setCalendarPopup(True)
        
        # Add done date field with default value option
        self.done_date_field = QDateEdit()
        self.done_date_field.setCalendarPopup(True)
        self.done_date_field.setSpecialValueText("Not completed (0)")
        self.done_date_field.setDate(QDate(1900, 1, 1))  # Default to a "null" date
        self.done_date_field.setMinimumDate(QDate(1900, 1, 1))
        
        self.vintage_years_field = QDoubleSpinBox()
        self.vintage_years_field.setMinimum(0)
        self.vintage_years_field.setMaximum(100)
        self.vintage_years_field.setDecimals(2)
        self.vintage_years_field.setSuffix(" years")
        
        self.meterage_kms_field = QDoubleSpinBox()
        self.meterage_kms_field.setMinimum(0)
        self.meterage_kms_field.setMaximum(1000000)
        self.meterage_kms_field.setDecimals(2)
        self.meterage_kms_field.setSuffix(" km")
        
        # Add status display
        self.status_display = StatusLabel("Unknown", "unknown")
        
        # Add fields to form
        form_layout.addRow("ID:", self.id_field)
        form_layout.addRow("Maintenance Type:", self.maintenance_type_field)
        form_layout.addRow("Next Due:", self.due_date_field)
        form_layout.addRow("Done Date:", self.done_date_field)
        form_layout.addRow("Vintage (Years):", self.vintage_years_field)
        form_layout.addRow("Meterage (km):", self.meterage_kms_field)
        form_layout.addRow("Status:", self.status_display)
        
        form_group.setLayout(form_layout)
        
        # Create buttons for form actions
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("Save")
        self.cancel_button = QPushButton("Cancel")
        
        self.save_button.clicked.connect(self.save_maintenance)
        self.cancel_button.clicked.connect(self.cancel_edit)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        # Add form and buttons to parent layout
        parent_layout.addWidget(form_group)
        parent_layout.addLayout(button_layout)
        
        # Initially disable all fields
        self.set_form_enabled(False)
    
    def update_equipment_tooltip(self):
        idx = self.equipment_field.currentIndex()
        if idx >= 0:
            self.equipment_field.setToolTip(self.equipment_field.currentText())
        else:
            self.equipment_field.setToolTip("")
        self.equipment_field.currentIndexChanged.connect(self.update_equipment_tooltip)
    
    # Removed update_due_date method - no longer needed without done date field
    
    # Removed toggle_due_date_calculation method - no longer needed without auto-calculate checkbox
    

    
    def load_data(self):
        """Load maintenance data for this category with enhanced status-based filtering."""
        logger.info(f"Loading maintenance data for category: {self.category}")
        
        try:
            # Get all maintenance records for this category (excluding archived)
            query = """
                SELECT m.*, e.make_and_type, e.ba_number
                FROM maintenance m
                JOIN equipment e ON m.equipment_id = e.equipment_id
                WHERE m.maintenance_category = %s
                AND (m.status != 'archived' OR m.status IS NULL)
                ORDER BY m.next_due_date, e.make_and_type
            """
            all_maintenance = database.execute_query(query, (self.category,))
            
            # Get the selected status filter
            status_filter = self.filter_status.currentText()
            
            # Load equipment data for dropdown
            self.load_equipment_data()
            
            # Set up table headers with Next Due instead of Due Date
            headers = ["ID", "BA Number", "Make & Type", "Type", "Done Date", "Next Due", "Status"]

            # Prepare data for table with status-based filtering
            data = []
            if all_maintenance:
                for maintenance in all_maintenance:
                    # Format done date
                    done_date_val = maintenance.get('done_date')
                    if isinstance(done_date_val, str):
                        try:
                            done_date_val = datetime.strptime(done_date_val, config.DATE_FORMAT).date()
                        except Exception:
                            done_date_val = None

                    done_date = utils.date_to_str(done_date_val) if done_date_val else "-"

                    # Calculate Next Due using standardized logic
                    next_due_date = None
                    if done_date_val:
                        # Use standardized next due date calculation for all categories
                        next_due_date = utils.calculate_next_due_date(done_date_val, self.category)
                    else:
                        # No done date, use original due_date as next due
                        due_date_val = maintenance.get('next_due_date') or maintenance.get('due_date')
                        if isinstance(due_date_val, str):
                            try:
                                next_due_date = datetime.strptime(due_date_val, config.DATE_FORMAT).date()
                            except Exception:
                                try:
                                    next_due_date = datetime.fromisoformat(due_date_val).date()
                                except Exception:
                                    next_due_date = None
                        else:
                            next_due_date = due_date_val

                    next_due = utils.date_to_str(next_due_date) if next_due_date else "-"

                    # Use centralized status calculation logic with distinction between manual and imported records
                    db_status = maintenance.get('status', '')

                    if db_status == 'completed':
                        # Manually completed records (via UI Complete button) - always show "completed"
                        status = 'completed'
                    elif done_date_val and next_due_date:
                        # Imported/legacy records with done_date but not manually completed
                        # Calculate time-based status for the NEXT maintenance cycle
                        maintenance_for_status = {
                            'status': 'scheduled',  # Reset status for next cycle calculation
                            'due_date': next_due_date.isoformat() if hasattr(next_due_date, 'isoformat') else str(next_due_date)
                        }
                        status = self.calculate_maintenance_status(maintenance_for_status)
                    else:
                        # Pending maintenance records - use original due_date and status for time-based calculation
                        maintenance_for_status = {
                            'status': maintenance.get('status', ''),
                            'due_date': maintenance.get('next_due_date') or maintenance.get('due_date')
                        }
                        status = self.calculate_maintenance_status(maintenance_for_status)

                    # Apply status-based filtering
                    if status_filter != "All":
                        if status.lower() != status_filter.lower():
                            continue  # Skip this record if it doesn't match the filter

                    # Add row data with BA Number and Make & Type
                    row_data = {
                        "ID": maintenance['maintenance_id'],
                        "BA Number": maintenance.get('ba_number', ''),
                        "Make & Type": maintenance.get('make_and_type', ''),
                        "Type": maintenance['maintenance_type'] or "",
                        "Done Date": done_date,
                        "Next Due": next_due,
                        "Status": status
                    }
                    data.append(row_data)

            # Sort by BA Number, then Make & Type
            data.sort(key=lambda x: (x["BA Number"], x["Make & Type"]))

            # Set table data (enhanced table handles filtering)
            self.maintenance_table.set_data(headers, data, id_column=0)

            # Hide the ID column
            self.maintenance_table.setColumnHidden(0, True)

            logger.info(f"Loaded {len(data)} maintenance records for {self.category}")
        except Exception as e:
            logger.error(f"Error loading maintenance data for {self.category}: {e}")
    
    def load_equipment_data(self):
        """Load equipment data for dropdown."""
        try:
            # Get active equipment
            equipment_list = Equipment.get_active()
            
            # Clear dropdown
            self.equipment_field.clear()
            
            # Add equipment to dropdown and cache
            if equipment_list:
                self.equipment_cache = {}
                for equipment in equipment_list:
                    equipment_id = equipment['equipment_id']
                    from utils import format_equipment_for_dropdown
                    equipment_name = format_equipment_for_dropdown(equipment)
                    self.equipment_field.addItem(equipment_name, equipment_id)
                    self.equipment_cache[equipment_id] = equipment
        except Exception as e:
            logger.error(f"Error loading equipment data: {e}")
    
    def maintenance_selected_enhanced(self, row_data):
        """Handle maintenance selection from enhanced paginated table."""
        try:
            if row_data and 'ID' in row_data:
                maintenance_id = row_data['ID']

                # Load maintenance details first to check status
                maintenance = Maintenance.get_by_id(maintenance_id)

                if maintenance:
                    # Enable all buttons when maintenance is selected
                    # Complete button is always enabled to give users full control
                    self.edit_button.setEnabled(True)
                    self.complete_button.setEnabled(True)  # Always enabled for user flexibility
                    self.delete_button.setEnabled(True)

                    # Load maintenance details
                    self.load_maintenance_details(maintenance_id)
                else:
                    # Disable all action buttons if maintenance not found
                    self.edit_button.setEnabled(False)
                    self.complete_button.setEnabled(False)
                    self.delete_button.setEnabled(False)
                    self.clear_form()
            else:
                # Disable all action buttons
                self.edit_button.setEnabled(False)
                self.complete_button.setEnabled(False)
                self.delete_button.setEnabled(False)

                # Clear form
                self.clear_form()

        except Exception as e:
            logger.error(f"Error in maintenance selection: {e}")

    def maintenance_selected(self, row):
        """Handle maintenance selection (legacy method for compatibility)."""
        # This method is kept for compatibility but should not be used with enhanced table
        pass
    
    def load_maintenance_details(self, maintenance_id):
        """Load maintenance details into the form with robust data validation."""
        try:
            # Get maintenance data with proper error handling
            maintenance = Maintenance.get_by_id(maintenance_id)
            
            if not maintenance:
                logger.error(f"No maintenance record found for ID: {maintenance_id}")
                self.clear_form()
                return
            
            logger.info(f"Loading maintenance details for ID {maintenance_id}: {maintenance}")
            
            # Set basic form values
            self.id_field.setText(str(maintenance.get('maintenance_id', '')))
            
            # Set equipment dropdown with validation
            equipment_id = maintenance.get('equipment_id')
            if equipment_id and hasattr(self, 'equipment_cache'):
                equipment = self.equipment_cache.get(equipment_id)
                if equipment:
                    from utils import format_equipment_for_dropdown
                    self.equipment_display.setText(format_equipment_for_dropdown(equipment))
                else:
                    self.equipment_display.setText("Unknown Equipment")
            else:
                self.equipment_display.setText("Unknown Equipment")
            
            # Set maintenance type with validation
            maintenance_type = maintenance.get('maintenance_type', '')
            if maintenance_type:
                index = self.maintenance_type_field.findText(maintenance_type)
                if index >= 0:
                    self.maintenance_type_field.setCurrentIndex(index)
                else:
                    # Add custom maintenance type if not in predefined list
                    self.maintenance_type_field.addItem(maintenance_type)
                    self.maintenance_type_field.setCurrentText(maintenance_type)
            else:
                self.maintenance_type_field.setCurrentIndex(0)
            
            # Handle done date with default value logic
            done_date_raw = maintenance.get('done_date')
            if done_date_raw:
                try:
                    # Parse done date from various formats
                    if isinstance(done_date_raw, str):
                        # Try different date formats
                        for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y', '%Y/%m/%d', '%d/%m/%Y']:
                            try:
                                done_date_parsed = datetime.strptime(done_date_raw, fmt).date()
                                qdate = QDate(done_date_parsed.year, done_date_parsed.month, done_date_parsed.day)
                                self.done_date_field.setDate(qdate)
                                break
                            except ValueError:
                                continue
                        else:
                            # Try ISO format parsing as fallback
                            try:
                                done_date_parsed = date.fromisoformat(done_date_raw)
                                qdate = QDate(done_date_parsed.year, done_date_parsed.month, done_date_parsed.day)
                                self.done_date_field.setDate(qdate)
                            except ValueError:
                                # Set to default "0" value
                                self.done_date_field.setDate(QDate(1900, 1, 1))
                    elif isinstance(done_date_raw, date):
                        qdate = QDate(done_date_raw.year, done_date_raw.month, done_date_raw.day)
                        self.done_date_field.setDate(qdate)
                    else:
                        # Set to default "0" value
                        self.done_date_field.setDate(QDate(1900, 1, 1))
                except Exception as e:
                    logger.error(f"Error parsing done date {done_date_raw}: {e}")
                    # Set to default "0" value
                    self.done_date_field.setDate(QDate(1900, 1, 1))
            else:
                # No done date in record - set to default "0" value
                self.done_date_field.setDate(QDate(1900, 1, 1))
            
            # Calculate and display Next Due based on Done Date and category interval
            next_due_date = None
            done_date_parsed = None
            
            # Parse done date if available
            if done_date_raw and done_date_raw != "":
                try:
                    if isinstance(done_date_raw, str):
                        for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y', '%Y/%m/%d', '%d/%m/%Y']:
                            try:
                                done_date_parsed = datetime.strptime(done_date_raw, fmt).date()
                                break
                            except ValueError:
                                continue
                        if not done_date_parsed:
                            done_date_parsed = date.fromisoformat(done_date_raw)
                    elif isinstance(done_date_raw, date):
                        done_date_parsed = done_date_raw
                except Exception:
                    done_date_parsed = None
            
            # Calculate Next Due using standardized logic
            if done_date_parsed:
                # Use standardized next due date calculation for all categories
                next_due_date = utils.calculate_next_due_date(done_date_parsed, self.category)
            else:
                # No done date, use original due_date as next due
                due_date_raw = maintenance.get('next_due_date') or maintenance.get('due_date')
                if due_date_raw:
                    try:
                        if isinstance(due_date_raw, str):
                            for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y', '%Y/%m/%d', '%d/%m/%Y']:
                                try:
                                    next_due_date = datetime.strptime(due_date_raw, fmt).date()
                                    break
                                except ValueError:
                                    continue
                            if not next_due_date:
                                next_due_date = date.fromisoformat(due_date_raw)
                        elif isinstance(due_date_raw, date):
                            next_due_date = due_date_raw
                    except Exception:
                        next_due_date = None
            
            # Set the Next Due field
            if next_due_date:
                qdate = QDate(next_due_date.year, next_due_date.month, next_due_date.day)
                self.due_date_field.setDate(qdate)
                logger.info(f"Set Next Due date: {next_due_date}")
            else:
                # Set to current date + category interval as default
                today = date.today()
                if self.category == 'TM-1':
                    default_due = today + timedelta(days=180)
                elif self.category == 'TM-2':
                    default_due = today + timedelta(days=365)
                else:
                    default_due = today + timedelta(days=90)  # 3 months default
                
                qdate = QDate(default_due.year, default_due.month, default_due.day)
                self.due_date_field.setDate(qdate)
                logger.info(f"Set default Next Due date: {default_due}")
            
            # Due date field is always editable (no auto-calculation)
            self.due_date_field.setReadOnly(False)
            logger.info("Due date field set to editable")
            
            # Set numeric fields with validation
            vintage_years = maintenance.get('vintage_years', 0)
            meterage_kms = maintenance.get('meterage_kms', 0)
            
            try:
                self.vintage_years_field.setValue(float(vintage_years) if vintage_years is not None else 0.0)
            except (ValueError, TypeError):
                self.vintage_years_field.setValue(0.0)
                logger.warning(f"Invalid vintage_years value: {vintage_years}")
            
            try:
                self.meterage_kms_field.setValue(float(meterage_kms) if meterage_kms is not None else 0.0)
            except (ValueError, TypeError):
                self.meterage_kms_field.setValue(0.0)
                logger.warning(f"Invalid meterage_kms value: {meterage_kms}")
            
            # Calculate and display status with proper logic
            status = self.calculate_maintenance_status(maintenance)
            self.status_display.setText(status.capitalize())
            self.status_display.setStatus(status)
            logger.info(f"Calculated status: {status}")
            
            # Set form fields to read-only
            self.set_form_enabled(False)
            
            logger.info(f"Successfully loaded maintenance details for ID {maintenance_id}")
            
        except Exception as e:
            logger.error(f"Error loading maintenance details for ID {maintenance_id}: {e}")
            self.clear_form()
            QMessageBox.critical(
                self,
                "Error Loading Details",
                f"Failed to load maintenance details:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def calculate_maintenance_status(self, maintenance):
        """Calculate maintenance status using centralized logic from utils."""
        from utils import calculate_maintenance_status
        return calculate_maintenance_status(maintenance)
    
    def clear_form(self):
        """Clear the maintenance form."""
        self.id_field.clear()
        self.equipment_field.setCurrentIndex(-1)
        self.maintenance_type_field.setCurrentIndex(0)
        # Set done date to default "0" value
        self.done_date_field.setDate(QDate(1900, 1, 1))
        self.due_date_field.setDate(QDate.currentDate().addMonths(3))
        self.vintage_years_field.setValue(0)
        self.meterage_kms_field.setValue(0)
        self.status_display.setText("Unknown")
        self.status_display.setStatus("unknown")
    
    def set_form_enabled(self, enabled):
        """Enable or disable form fields."""
        self.equipment_field.setVisible(enabled)
        self.equipment_display.setVisible(not enabled)
        
        # Due date field is always editable when form is enabled
        self.due_date_field.setReadOnly(not enabled)
        
        self.vintage_years_field.setReadOnly(not enabled)
        self.meterage_kms_field.setReadOnly(not enabled)
        
        self.save_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)
    
    def add_maintenance(self):
        """Add new maintenance."""
        # Clear form
        self.clear_form()
        
        # Enable form fields
        self.set_form_enabled(True)
        
        # Focus on first field
        self.equipment_field.setFocus()
    
    def edit_maintenance(self):
        """Edit selected maintenance."""
        # Enable form fields
        self.set_form_enabled(True)
        
        # Focus on first editable field
        self.maintenance_type_field.setFocus()
    
    def complete_maintenance(self):
        """Complete selected maintenance."""
        maintenance_id = self.id_field.text()
        
        if not maintenance_id:
            return
        
        # Get maintenance data
        maintenance = Maintenance.get_by_id(int(maintenance_id))
        
        if not maintenance:
            QMessageBox.critical(
                self,
                "Error",
                "Selected maintenance record not found.",
                QMessageBox.StandardButton.Ok
            )
            return
        
        # Allow users to complete maintenance multiple times or update completion details
        # PREVENT multiple completion of same record
        if maintenance.get('status') == 'completed':
            QMessageBox.warning(
                self, 
                "Already Completed", 
                "This maintenance is already completed. Cannot complete again."
            )
            return
        
        # Open completion dialog
        from ui.dialogs import MaintenanceCompletionDialog
        dialog = MaintenanceCompletionDialog(maintenance, parent=self)
        
        if dialog.exec_() == QDialog.Accepted:
            try:
                # Get completion data
                completion_data = dialog.get_completion_data()
                
                # Create maintenance object with corrected field names
                maintenance_obj = Maintenance(
                    maintenance_id=int(maintenance_id),
                    equipment_id=maintenance['equipment_id'],
                    maintenance_type=maintenance['maintenance_type'],
                    done_date=maintenance['done_date'],  # Keep original!
                    next_due_date=maintenance.get('next_due_date') or maintenance.get('next_due_date') or maintenance.get('due_date'),  # Handle both old and new
                    vintage_years=maintenance['vintage_years'],
                    meterage_kms=maintenance['meterage_kms'],
                    maintenance_category=maintenance.get('maintenance_category', self.category)
                )
                
                # Complete the maintenance (creates new record automatically)
                result = maintenance_obj.complete_maintenance(
                    actual_completion_date=completion_data['completion_date'],
                    completion_meterage=completion_data['completion_meterage'],
                    notes=completion_data['notes'],
                    completed_by="Current User"  # TODO: Get actual user
                )
                
                if result:
                    # Update equipment meterage if requested
                    if completion_data['update_equipment_meterage']:
                        self.update_equipment_meterage(
                            maintenance['equipment_id'],
                            completion_data['completion_meterage']
                        )
                    
                    # Create fluid demand forecasts if requested
                    demand_summary = ""
                    if completion_data['create_demand'] and completion_data['selected_fluids']:
                        # Create demands and collect detailed summary
                        demand_summary = self.create_fluid_demand_forecasts_with_summary(
                            completion_data['selected_fluids'],
                            completion_data['fiscal_year']
                        )
                    
                    # Show enhanced success message with detailed demand information
                    success_message = f"Maintenance completed successfully!\n\n"
                    success_message += f"✅ Current maintenance marked as completed\n"
                    success_message += f"✅ Next maintenance automatically scheduled\n"
                    success_message += f"✅ Maintenance chain preserved\n\n"
                    success_message += f"Completion Date: {completion_data['completion_date']}\n"
                    success_message += f"Meterage: {completion_data['completion_meterage']} km\n"
                    
                    if completion_data['update_equipment_meterage']:
                        success_message += f"✅ Equipment meterage updated\n"
                    
                    if completion_data['create_demand'] and completion_data['selected_fluids']:
                        success_message += f"\n{demand_summary}"
                    
                    QMessageBox.information(
                        self,
                        "Success",
                        success_message,
                        QMessageBox.StandardButton.Ok
                    )
                    
                    # Set filter to "All" to ensure completed maintenance remains visible
                    self.filter_status.setCurrentText("All")
                    
                    # Reload data and update display with forced table refresh
                    self.load_data()
                    
                    # Force table widget to refresh its data
                    if hasattr(self, 'table_widget') and hasattr(self.table_widget, 'refresh_data'):
                        self.table_widget.refresh_data()
                    
                    # Reload maintenance details to update the form display
                    self.load_maintenance_details(int(maintenance_id))
                    
                    # Force a complete UI update
                    self.update()
                    
                    # Also refresh the parent dashboard if available
                    try:
                        parent_window = self.window()
                        while parent_window:
                            if hasattr(parent_window, 'dashboard_widget'):
                                parent_window.dashboard_widget.load_data()
                                break
                            parent_window = parent_window.parent()
                    except Exception as e:
                        logger.debug(f"Could not refresh dashboard: {e}")
                    
                    # Reload data to refresh status, but keep Complete button enabled
                    # This allows users to complete maintenance multiple times if needed
                else:
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Failed to complete maintenance. Please try again.",
                        QMessageBox.StandardButton.Ok
                    )
            except Exception as e:
                logger.error(f"Error completing maintenance: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"An error occurred while completing maintenance:\n{str(e)}",
                    QMessageBox.StandardButton.Ok
                )
    
    def update_equipment_meterage(self, equipment_id, new_meterage):
        """Update equipment meterage."""
        try:
            from models import Equipment
            query = """
                UPDATE equipment 
                SET meterage_kms = %s 
                WHERE equipment_id = %s
            """
            database.execute_query(query, (new_meterage, equipment_id))
            logger.info(f"Updated equipment {equipment_id} meterage to {new_meterage}")
        except Exception as e:
            logger.error(f"Error updating equipment meterage: {e}")
    
    def create_fluid_demand_forecasts_with_summary(self, selected_fluids, fiscal_year):
        """Create demand forecast records and return detailed summary for success message."""
        try:
            from models import DemandForecast
            
            created_count = 0
            total_demands_created = 0
            demand_details = []
            
            # Define option labels for summary
            option_labels = {
                'full_only': 'Full Change',
                'topup_only': '10% Top-up Only',
                'full_plus_topup': 'Full Change + 10% Top-up'
            }
            
            for fluid in selected_fluids:
                # Get equipment data for calculations
                equipment = Equipment.get_by_id(fluid['equipment_id'])
                
                if equipment:
                    # Get fluid details
                    capacity = fluid.get('capacity_ltrs_kg', 0)
                    top_up = fluid.get('addl_10_percent_top_up', 0)
                    units_held = equipment.units_held
                    demand_option = fluid.get('demand_option', 'full_only')
                    fluid_type = fluid.get('fluid_type', 'Unknown')
                    unit = fluid.get('accounting_unit', 'Ltr')
                    
                    logger.info(f"Processing fluid: {fluid_type}, Option: {demand_option}")
                    
                    demands_for_this_fluid = 0
                    
                    # Create demands based on selected option
                    if demand_option == 'full_only':
                        total_requirement = capacity * units_held
                        if total_requirement <= 0:
                            total_requirement = capacity if capacity > 0 else 1.0
                        
                        success = self._create_single_demand(
                            fluid, fiscal_year, total_requirement, 
                            f"Full fluid change - {self.category} maintenance"
                        )
                        if success:
                            demands_for_this_fluid = 1
                            total_demands_created += 1
                    
                    elif demand_option == 'topup_only':
                        total_requirement = top_up * units_held
                        if total_requirement <= 0:
                            total_requirement = top_up if top_up > 0 else 0.1
                        
                        success = self._create_single_demand(
                            fluid, fiscal_year, total_requirement, 
                            f"10% top-up only - {self.category} maintenance"
                        )
                        if success:
                            demands_for_this_fluid = 1
                            total_demands_created += 1
                    
                    elif demand_option == 'full_plus_topup':
                        # Create TWO separate demand records
                        full_requirement = capacity * units_held
                        if full_requirement <= 0:
                            full_requirement = capacity if capacity > 0 else 1.0
                        
                        success1 = self._create_single_demand(
                            fluid, fiscal_year, full_requirement, 
                            f"Full fluid change - {self.category} maintenance"
                        )
                        
                        topup_requirement = top_up * units_held
                        if topup_requirement <= 0:
                            topup_requirement = top_up if top_up > 0 else 0.1
                        
                        success2 = self._create_single_demand(
                            fluid, fiscal_year, topup_requirement, 
                            f"10% additional top-up - {self.category} maintenance"
                        )
                        
                        demands_for_this_fluid = (1 if success1 else 0) + (1 if success2 else 0)
                        total_demands_created += demands_for_this_fluid
                    
                    # Add to summary if any demands were created
                    if demands_for_this_fluid > 0:
                        option_label = option_labels.get(demand_option, 'Unknown')
                        if demand_option == 'full_plus_topup':
                            detail = f"  • {fluid_type}: {option_label} ({demands_for_this_fluid} demands)"
                        else:
                            detail = f"  • {fluid_type}: {option_label} (1 demand)"
                        demand_details.append(detail)
                    
                    created_count += 1
                else:
                    logger.warning(f"Equipment not found for fluid {fluid.get('fluid_type', 'Unknown')}")
            
            # Create summary message
            if total_demands_created > 0:
                summary = f"🎯 Fluid Demand Forecasts Created ({total_demands_created} total):\n"
                summary += "\n".join(demand_details)
                summary += f"\n\n📅 Fiscal Year: {fiscal_year}"
                logger.info(f"Demand creation summary: {created_count} fluids processed, {total_demands_created} demands created")
                return summary
            else:
                return "❌ No fluid demands were created (check fluid configuration)"
                
        except Exception as e:
            logger.error(f"Error creating fluid demand forecasts with summary: {e}")
            return f"⚠️ Partial success creating fluid demands: {str(e)}"
    
    def create_fluid_demand_forecasts(self, selected_fluids, fiscal_year):
        """Create demand forecast records for selected fluids with enhanced options."""
        try:
            from models import DemandForecast
            
            created_count = 0
            total_demands_created = 0
            
            for fluid in selected_fluids:
                # Get equipment data for calculations
                equipment = Equipment.get_by_id(fluid['equipment_id'])
                
                if equipment:
                    # Get fluid details
                    capacity = fluid.get('capacity_ltrs_kg', 0)
                    top_up = fluid.get('addl_10_percent_top_up', 0)
                    units_held = equipment.units_held  # Access attribute directly, not via get()
                    demand_option = fluid.get('demand_option', 'full_only')
                    fluid_type = fluid.get('fluid_type', 'Unknown')
                    
                    logger.info(f"Processing fluid: {fluid_type}, Option: {demand_option}, "
                               f"Capacity: {capacity}, Top-up: {top_up}, Units: {units_held}")
                    
                    # Create demands based on selected option
                    if demand_option == 'full_only':
                        # Create single demand for full capacity only
                        total_requirement = capacity * units_held
                        if total_requirement <= 0:
                            total_requirement = capacity if capacity > 0 else 1.0
                        
                        success = self._create_single_demand(
                            fluid, fiscal_year, total_requirement, 
                            f"Full fluid change - {self.category} maintenance"
                        )
                        if success:
                            total_demands_created += 1
                            logger.info(f"Created full change demand: {total_requirement} units")
                    
                    elif demand_option == 'topup_only':
                        # Create single demand for 10% top-up only
                        total_requirement = top_up * units_held
                        if total_requirement <= 0:
                            total_requirement = top_up if top_up > 0 else 0.1  # Minimal fallback
                        
                        success = self._create_single_demand(
                            fluid, fiscal_year, total_requirement, 
                            f"10% top-up only - {self.category} maintenance"
                        )
                        if success:
                            total_demands_created += 1
                            logger.info(f"Created 10% top-up demand: {total_requirement} units")
                    
                    elif demand_option == 'full_plus_topup':
                        # Create TWO separate demand records
                        demands_created_for_fluid = 0
                        
                        # 1. Full fluid change demand
                        full_requirement = capacity * units_held
                        if full_requirement <= 0:
                            full_requirement = capacity if capacity > 0 else 1.0
                        
                        success1 = self._create_single_demand(
                            fluid, fiscal_year, full_requirement, 
                            f"Full fluid change - {self.category} maintenance"
                        )
                        if success1:
                            demands_created_for_fluid += 1
                            logger.info(f"Created full change demand: {full_requirement} units")
                        
                        # 2. Additional 10% top-up demand
                        topup_requirement = top_up * units_held
                        if topup_requirement <= 0:
                            topup_requirement = top_up if top_up > 0 else 0.1
                        
                        success2 = self._create_single_demand(
                            fluid, fiscal_year, topup_requirement, 
                            f"10% additional top-up - {self.category} maintenance"
                        )
                        if success2:
                            demands_created_for_fluid += 1
                            logger.info(f"Created additional top-up demand: {topup_requirement} units")
                        
                        total_demands_created += demands_created_for_fluid
                        
                        if demands_created_for_fluid == 2:
                            logger.info(f"Successfully created both demands for {fluid_type}")
                        else:
                            logger.warning(f"Only created {demands_created_for_fluid}/2 demands for {fluid_type}")
                    
                    else:
                        logger.warning(f"Unknown demand option '{demand_option}' for fluid {fluid_type}, defaulting to full_only")
                        # Fallback to full_only
                        total_requirement = capacity * units_held
                        if total_requirement <= 0:
                            total_requirement = capacity if capacity > 0 else 1.0
                        
                        success = self._create_single_demand(
                            fluid, fiscal_year, total_requirement, 
                            f"Full fluid change (fallback) - {self.category} maintenance"
                        )
                        if success:
                            total_demands_created += 1
                    
                    created_count += 1  # Count processed fluids
                else:
                    logger.warning(f"Equipment not found for fluid {fluid.get('fluid_type', 'Unknown')}")
            
            logger.info(f"Processed {created_count} fluids, created {total_demands_created} demand forecast records for fiscal year {fiscal_year}")
            
        except Exception as e:
            logger.error(f"Error creating fluid demand forecasts: {e}")
            QMessageBox.warning(
                self,
                "Partial Success",
                f"Maintenance completed but there was an issue creating some fluid demand forecasts:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def _create_single_demand(self, fluid, fiscal_year, total_requirement, description):
        """Create a single demand forecast record with proper error handling."""
        try:
            from models import DemandForecast
            
            demand = DemandForecast(
                fluid_id=fluid['fluid_id'],
                fiscal_year=fiscal_year,
                total_requirement=total_requirement,
                remarks=description
            )
            
            demand_id = demand.save()
            if demand_id:
                logger.info(f"Successfully created demand forecast ID {demand_id}: {description}")
                return True
            else:
                logger.error(f"Failed to save demand forecast: {description}")
                return False
                
        except Exception as e:
            logger.error(f"Error creating single demand forecast: {e}")
            return False
    
    def delete_maintenance(self):
        """Delete selected maintenance."""
        maintenance_id = self.id_field.text()
        
        if not maintenance_id:
            return
        
        # Confirm deletion
        confirm = QMessageBox.question(
            self,
            "Confirm Deletion",
            "Are you sure you want to delete this maintenance record?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if confirm == QMessageBox.StandardButton.Yes:
            try:
                # Delete maintenance
                result = Maintenance.delete(int(maintenance_id))
                
                if result:
                    # Show success message
                    QMessageBox.information(
                        self,
                        "Success",
                        "Maintenance record deleted successfully.",
                        QMessageBox.StandardButton.Ok
                    )
                    
                    # Reload data
                    self.load_data()
                    
                    # Clear form
                    self.clear_form()
                    
                    # Disable edit and delete buttons
                    self.edit_button.setEnabled(False)
                    self.delete_button.setEnabled(False)
                else:
                    # Show error message
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Failed to delete maintenance record. Please try again.",
                        QMessageBox.StandardButton.Ok
                    )
            except Exception as e:
                logger.error(f"Error deleting maintenance: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"An error occurred: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
    
    def save_maintenance(self):
        """Save maintenance data."""
        # Get form values
        maintenance_id = self.id_field.text()
        equipment_id = self.equipment_field.currentData()
        maintenance_type = self.maintenance_type_field.currentText()
        
        # Get due date from field
        due_date_qdate = self.due_date_field.date()
        due_date = date(due_date_qdate.year(), due_date_qdate.month(), due_date_qdate.day())
        
        # Get done date from field - handle default "0" value
        done_date_qdate = self.done_date_field.date()
        if done_date_qdate.year() == 1900:  # Default "0" value
            done_date = None
        else:
            done_date = date(done_date_qdate.year(), done_date_qdate.month(), done_date_qdate.day())
        
        vintage_years = self.vintage_years_field.value()
        meterage_kms = self.meterage_kms_field.value()
        
        # Validate form
        if not equipment_id or not maintenance_type:
            QMessageBox.warning(
                self,
                "Validation Error",
                "Equipment and Maintenance Type are required fields.",
                QMessageBox.StandardButton.Ok
            )
            return
        
        try:
            # Create maintenance object
            maintenance = Maintenance(
                maintenance_id=int(maintenance_id) if maintenance_id else None,
                equipment_id=equipment_id,
                maintenance_type=maintenance_type,
                done_date=done_date,
                next_due_date=due_date,
                vintage_years=vintage_years,
                meterage_kms=meterage_kms,
                maintenance_category=self.category
            )
            
            # Save maintenance
            result = maintenance.save()
            
            if result:
                # Show success message
                QMessageBox.information(
                    self,
                    "Success",
                    "Maintenance record saved successfully.",
                    QMessageBox.StandardButton.Ok
                )
                
                # Reload data
                self.load_data()
                
                # Load the saved maintenance details
                self.load_maintenance_details(result)
                
                # Disable form fields
                self.set_form_enabled(False)
                
                # Enable edit and delete buttons
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
            else:
                # Show error message
                QMessageBox.critical(
                    self,
                    "Error",
                    "Failed to save maintenance record. Please try again.",
                    QMessageBox.StandardButton.Ok
                )
        except Exception as e:
            logger.error(f"Error saving maintenance: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def cancel_edit(self):
        """Cancel maintenance edit."""
        # Get current maintenance ID
        maintenance_id = self.id_field.text()
        
        if maintenance_id:
            # Reload maintenance details
            self.load_maintenance_details(int(maintenance_id))
        else:
            # Clear form
            self.clear_form()
        
        # Disable form fields
        self.set_form_enabled(False)
    
    def create_maintenance_from_equipment(self, equipment_id, maintenance_category):
        """Create new maintenance record from equipment selection."""
        try:
            # Clear and enable form
            self.clear_form()
            self.set_form_enabled(True)
            
            # Set equipment
            index = self.equipment_field.findData(equipment_id)
            if index >= 0:
                self.equipment_field.setCurrentIndex(index)
            
            # Set default maintenance type for this category
            maintenance_types = config.MAINTENANCE_TYPES.get(maintenance_category, ["Other"])
            if maintenance_types:
                self.maintenance_type_field.setCurrentText(maintenance_types[0])
            
            # Focus on maintenance type field
            self.maintenance_type_field.setFocus()
            
        except Exception as e:
            logger.error(f"Error creating maintenance from equipment: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to create maintenance record: {str(e)}",
                QMessageBox.StandardButton.Ok
            )

    def archive_completed(self):
        """Archive completed maintenance records."""
        from datetime import datetime, date
        from calendar import month_name
        import json
        from models import MaintenanceArchive, Maintenance
        from PyQt5.QtWidgets import QInputDialog, QMessageBox
        
        try:
            # Get completed maintenance records for this category
            completed_records = Maintenance.get_completed_by_category(self.category)
            
            if not completed_records:
                QMessageBox.information(
                    self,
                    "No Records",
                    f"No completed maintenance records found for {self.category} category.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            # Determine date range for archive
            if not completed_records:
                return
            
            # Find date range of completed records
            dates = []
            for record in completed_records:
                completion_date = record.get('actual_completion_date') or record.get('done_date')
                if completion_date:
                    if isinstance(completion_date, str):
                        try:
                            dates.append(datetime.strptime(completion_date, '%Y-%m-%d').date())
                        except:
                            continue
                    else:
                        dates.append(completion_date)
            
            if not dates:
                QMessageBox.information(
                    self,
                    "No Dates",
                    "No valid completion dates found in the records.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            min_date = min(dates)
            max_date = max(dates)
            
            # Create archive dialog
            archive_dialog = ArchiveDialog(self.category, completed_records, min_date, max_date, self)
            if archive_dialog.exec_() == archive_dialog.Accepted:
                archive_data = archive_dialog.get_archive_data()
                
                if archive_data:
                    # Create and save the archive
                    archive = MaintenanceArchive(
                        archive_name=archive_data['name'],
                        archive_type=archive_data['type'],
                        maintenance_category=self.category,
                        period_start=archive_data['period_start'],
                        period_end=archive_data['period_end'],
                        created_by=archive_data.get('created_by', 'System'),
                        notes=archive_data.get('notes', '')
                    )
                    
                    # Set archived records
                    records_to_archive = archive_data['records']
                    archive.set_archived_records(records_to_archive)
                    
                    # Save archive
                    archive_id = archive.save()
                    
                    if archive_id:
                        # Mark records as archived (optional - keep them visible but mark as archived)
                        record_ids = [record['maintenance_id'] for record in records_to_archive]
                        archived_count = Maintenance.archive_records(record_ids)
                        
                        QMessageBox.information(
                            self,
                            "Archive Created",
                            f"Successfully created archive '{archive_data['name']}'.\n\n"
                            f"Archived {len(records_to_archive)} maintenance records.\n"
                            f"You can view this archive in the History tab.",
                            QMessageBox.StandardButton.Ok
                        )
                        
                        # Reload data to refresh the view
                        self.load_data()
                        
                        # Switch to history tab if parent has the method
                        main_window = self.window()
                        if hasattr(main_window, 'tab_widget'):
                            # Find the maintenance tab
                            for i in range(main_window.tab_widget.count()):
                                widget = main_window.tab_widget.widget(i)
                                if hasattr(widget, 'tab_widget'):  # This is the maintenance widget
                                    # Switch to history tab (index 3)
                                    widget.tab_widget.setCurrentIndex(3)
                                    # Load history data
                                    history_widget = widget.tab_widget.widget(3)
                                    if hasattr(history_widget, 'load_data'):
                                        history_widget.load_data()
                                    break
                    else:
                        QMessageBox.critical(
                            self,
                            "Archive Failed",
                            "Failed to create archive. Please try again.",
                            QMessageBox.StandardButton.Ok
                        )
                        
        except Exception as e:
            logger.error(f"Error archiving completed records: {e}")
            QMessageBox.critical(
                self,
                "Archive Error",
                f"An error occurred while creating the archive:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )

class ArchiveDialog(QDialog):
    """Dialog for creating maintenance archives."""
    
    def __init__(self, category, records, min_date, max_date, parent=None):
        super().__init__(parent)
        self.category = category
        self.records = records
        self.min_date = min_date
        self.max_date = max_date
        
        self.setWindowTitle(f"Archive {category} Maintenance Records")
        self.setModal(True)
        self.resize(500, 400)
        
        self.setup_ui()
        self.populate_defaults()
    
    def setup_ui(self):
        """Set up the archive dialog UI."""
        from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QFormLayout, 
                                   QLineEdit, QComboBox, QDateEdit, QTextEdit,
                                   QCheckBox, QSpinBox, QLabel, QGroupBox,
                                   QDialogButtonBox)
        from PyQt5.QtCore import QDate
        
        layout = QVBoxLayout(self)
        
        # Archive info section
        info_group = QGroupBox("Archive Information")
        info_layout = QFormLayout()
        
        # Archive name
        self.name_edit = QLineEdit()
        info_layout.addRow("Archive Name:", self.name_edit)
        
        # Archive type
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Monthly", "Yearly", "TM-1 (Half Yearly)", "TM-2 (Yearly)", "Custom"])
        self.type_combo.currentTextChanged.connect(self.update_archive_name)
        info_layout.addRow("Archive Type:", self.type_combo)
        
        # Period selection
        period_layout = QHBoxLayout()
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate(self.min_date))
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate(self.max_date))
        
        period_layout.addWidget(QLabel("From:"))
        period_layout.addWidget(self.start_date_edit)
        period_layout.addWidget(QLabel("To:"))
        period_layout.addWidget(self.end_date_edit)
        
        info_layout.addRow("Period:", period_layout)
        
        # Created by
        self.created_by_edit = QLineEdit()
        self.created_by_edit.setPlaceholderText("Enter your name (optional)")
        info_layout.addRow("Created By:", self.created_by_edit)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # Record selection section
        records_group = QGroupBox("Records to Archive")
        records_layout = QVBoxLayout()
        
        # Info about records
        self.records_info_label = QLabel(f"Found {len(self.records)} completed maintenance records.")
        records_layout.addWidget(self.records_info_label)
        
        # Include all option
        self.include_all_check = QCheckBox("Include all completed records")
        self.include_all_check.setChecked(True)
        records_layout.addWidget(self.include_all_check)
        
        records_group.setLayout(records_layout)
        layout.addWidget(records_group)
        
        # Notes section
        notes_group = QGroupBox("Archive Notes")
        notes_layout = QVBoxLayout()
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("Optional notes about this archive...")
        
        notes_layout.addWidget(self.notes_edit)
        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)
        
        # Dialog buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def populate_defaults(self):
        """Populate default values."""
        from datetime import datetime
        from calendar import month_name
        
        # Default archive name based on category and current date
        current_date = datetime.now()
        
        if self.category in ['Monthly']:
            # Monthly archive: "March 2025 Monthly Maintenance"
            month = month_name[current_date.month]
            year = current_date.year
            default_name = f"{month} {year} Monthly Maintenance"
            self.type_combo.setCurrentText("Monthly")
        elif self.category in ['Yearly']:
            # Yearly archive: "2025 Yearly Maintenance"
            year = current_date.year
            default_name = f"{year} Yearly Maintenance"
            self.type_combo.setCurrentText("Yearly")
        elif self.category == 'TM-1':
            # TM-1 archive: "March 2025 TM-1 Maintenance"
            month = month_name[current_date.month]
            year = current_date.year
            default_name = f"{month} {year} TM-1 Maintenance"
            self.type_combo.setCurrentText("TM-1 (Half Yearly)")
        elif self.category == 'TM-2':
            # TM-2 archive: "March 2025 TM-2 Maintenance"
            month = month_name[current_date.month]
            year = current_date.year
            default_name = f"{month} {year} TM-2 Maintenance"
            self.type_combo.setCurrentText("TM-2 (Yearly)")
        else:
            # Other categories: "March 2025 [Category] Maintenance"
            month = month_name[current_date.month]
            year = current_date.year
            default_name = f"{month} {year} {self.category} Maintenance"
            self.type_combo.setCurrentText("Custom")
        
        self.name_edit.setText(default_name)
    
    def update_archive_name(self):
        """Update archive name when type changes."""
        from datetime import datetime
        from calendar import month_name
        
        archive_type = self.type_combo.currentText()
        current_date = datetime.now()
        
        if archive_type == "Monthly":
            month = month_name[current_date.month]
            year = current_date.year
            new_name = f"{month} {year} Monthly Maintenance"
        elif archive_type == "Yearly":
            year = current_date.year
            new_name = f"{year} Yearly Maintenance"
        elif archive_type == "TM-1 (Half Yearly)":
            month = month_name[current_date.month]
            year = current_date.year
            new_name = f"{month} {year} TM-1 Maintenance"
        elif archive_type == "TM-2 (Yearly)":
            month = month_name[current_date.month]
            year = current_date.year
            new_name = f"{month} {year} TM-2 Maintenance"
        else:  # Custom
            month = month_name[current_date.month]
            year = current_date.year
            new_name = f"{month} {year} {self.category} Maintenance"
        
        self.name_edit.setText(new_name)
    
    def get_archive_data(self):
        """Get the archive data from the dialog."""
        if not self.name_edit.text().strip():
            QMessageBox.warning(
                self,
                "Invalid Input",
                "Please enter an archive name.",
                QMessageBox.StandardButton.Ok
            )
            return None
        
        # Determine which records to include
        if self.include_all_check.isChecked():
            records_to_archive = self.records
        else:
            # For now, include all - could add filtering later
            records_to_archive = self.records
        
        # Map display text to database-friendly type values
        archive_type = self.type_combo.currentText()
        type_mapping = {
            "Monthly": "monthly",
            "Yearly": "yearly", 
            "TM-1 (Half Yearly)": "tm-1",
            "TM-2 (Yearly)": "tm-2",
            "Custom": "custom"
        }
        
        return {
            'name': self.name_edit.text().strip(),
            'type': type_mapping.get(archive_type, archive_type.lower()),
            'period_start': self.start_date_edit.date().toPyDate(),
            'period_end': self.end_date_edit.date().toPyDate(),
            'created_by': self.created_by_edit.text().strip() or 'System',
            'notes': self.notes_edit.toPlainText().strip(),
            'records': records_to_archive
        }
